# 🌐 真正的多机分布式部署操作指南

## 📋 前提条件

### 网络环境
- **主机IP:** `***********` (有数据文件的电脑)
- **从机IP:** `192.168.1.x` (另一台电脑)
- **要求:** 两台电脑在同一局域网内

### 软件环境
两台电脑都需要安装：
```bash
pip install dask[complete] distributed dask-ml pyarrow fastparquet pandas numpy scikit-learn matplotlib seaborn plotly
```

---

## 🚀 分步操作指南

### 第一步：主机操作 (***********)

#### 1.1 启动Dask调度器
```bash
# 在主机上打开命令行，输入：
dask-scheduler --host 0.0.0.0 --port 8786 --dashboard-address 8787
```

**预期输出：**
```
distributed.scheduler - INFO - -----------------------------------------------
distributed.scheduler - INFO - Scheduler at: tcp://***********:8786
distributed.scheduler - INFO - dashboard at: http://***********:8787/status
distributed.scheduler - INFO - -----------------------------------------------
```

#### 1.2 验证调度器启动成功
- 浏览器访问：`http://***********:8787/status`
- 应该看到Dask仪表板界面

---

### 第二步：从机操作 (另一台电脑)

#### 2.1 连接到主机调度器
```bash
# 在从机上打开命令行，输入：
dask-worker tcp://***********:8786 --memory-limit 2GB --nthreads 2
```

**预期输出：**
```
distributed.worker - INFO - Start worker at: tcp://192.168.1.x:xxxxx
distributed.worker - INFO - Registered to: tcp://***********:8786
distributed.worker - INFO - -----------------------------------------------
```

#### 2.2 验证工作节点连接成功
- 回到主机浏览器刷新仪表板
- 应该看到新增了一个工作节点

---

### 第三步：主机运行分析 (***********)

#### 3.1 运行分布式分析
```bash
# 在主机上新开一个命令行窗口，输入：
python main_pipeline.py --mode multi-machine --scheduler tcp://***********:8786
```

#### 3.2 监控分析进度
- 仪表板地址：`http://***********:8787/status`
- 观察任务在不同机器间的分布情况

---

## 🔧 使用项目内置工具的方式

### 主机操作序列

#### 步骤1：启动调度器
```bash
python start_distributed.py --mode scheduler
```

#### 步骤2：运行分析
```bash
# 等从机连接后，在主机上执行：
python start_distributed.py --mode analysis --scheduler tcp://***********:8786
```

### 从机操作序列

#### 步骤1：连接工作节点
```bash
python start_distributed.py --mode worker --scheduler tcp://***********:8786
```

---

## 📊 完整的操作时序

### 时间线操作步骤

| 时间 | 主机 (***********) | 从机 (192.168.1.x) |
|------|-------------------|-------------------|
| T1 | `dask-scheduler --host 0.0.0.0 --port 8786 --dashboard-address 8787` | 等待 |
| T2 | 验证仪表板可访问 | `dask-worker tcp://***********:8786 --memory-limit 2GB --nthreads 2` |
| T3 | 确认工作节点已连接 | 保持工作节点运行 |
| T4 | `python main_pipeline.py --mode multi-machine --scheduler tcp://***********:8786` | 继续运行工作节点 |
| T5 | 监控分析进度 | 观察CPU/内存使用情况 |

---

## 🎯 验证真正分布式的标志

### 在仪表板中确认
1. **工作节点数量：** 显示 > 1 个工作节点
2. **不同IP地址：** 工作节点来自不同IP
3. **任务分布：** 任务在不同机器间分配
4. **内存使用：** 总内存 = 各机器内存之和

### 在系统监控中确认
```bash
# 主机监控 (主要负责数据I/O)
htop  # 查看CPU和内存使用

# 从机监控 (主要负责计算)
htop  # 应该看到计算任务
```

---

## ⚠️ 故障排除

### 如果从机连接失败

#### 检查网络连通性
```bash
# 在从机上测试：
ping ***********
telnet *********** 8786
```

#### 检查防火墙设置
```bash
# 在主机上执行：
netsh advfirewall firewall add rule name="Dask Scheduler" dir=in action=allow protocol=TCP localport=8786
netsh advfirewall firewall add rule name="Dask Dashboard" dir=in action=allow protocol=TCP localport=8787
```

### 如果分析运行失败

#### 回退到本地模式
```bash
# 在主机上执行：
python main_pipeline.py --mode local
```

---

## 🎉 成功标志

当您看到以下输出时，说明多机分布式部署成功：

```
🎉 真正的分布式音乐数据分析完成!
📊 分布式分析摘要:
数据规模: 119,988 条记录 (全部12万条)
分布式特性: 真正多机分布式处理
工作节点: 2 个 (来自不同IP)
数据分区: X 个
集群仪表板: http://***********:8787/status
```

**恭喜！您已成功部署真正的多机分布式音乐数据分析系统！**
