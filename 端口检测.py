#!/usr/bin/env python3
"""
简化的端口检测工具
快速检测多机分布式部署状态
"""

import socket
import subprocess
import sys
import requests
from dask.distributed import Client

def check_port(host, port, timeout=3):
    """检查端口是否可达"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def get_local_ip():
    """获取本机IP"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def check_multi_machine(scheduler_address="tcp://***********:8786"):
    """检查是否为多机部署"""
    
    print("🔍 多机分布式检测")
    print("=" * 40)
    
    # 解析调度器地址
    scheduler_ip = scheduler_address.split("://")[1].split(":")[0]
    scheduler_port = int(scheduler_address.split(":")[-1])
    dashboard_port = 8787
    
    local_ip = get_local_ip()
    print(f"🏠 本机IP: {local_ip}")
    print(f"🎯 目标调度器: {scheduler_ip}:{scheduler_port}")
    
    # 检查端口连通性
    print("\n📡 端口连通性检查:")
    scheduler_ok = check_port(scheduler_ip, scheduler_port)
    dashboard_ok = check_port(scheduler_ip, dashboard_port)
    
    print(f"  调度器端口 ({scheduler_port}): {'✅' if scheduler_ok else '❌'}")
    print(f"  仪表板端口 ({dashboard_port}): {'✅' if dashboard_ok else '❌'}")
    
    if not scheduler_ok:
        print("\n❌ 调度器不可达！")
        print("💡 请确保:")
        print("   1. 调度器已启动")
        print("   2. IP地址正确")
        print("   3. 防火墙允许连接")
        return False
    
    # 检查集群工作节点
    print("\n👥 工作节点检查:")
    try:
        client = Client(scheduler_address, timeout=10)
        workers = client.scheduler_info()['workers']
        
        worker_ips = set()
        for worker_id, worker_info in workers.items():
            worker_host = worker_info['host']
            worker_ips.add(worker_host)
            print(f"  🖥️  {worker_host} (线程: {worker_info.get('nthreads', 0)})")
        
        client.close()
        
        # 判断是否多机
        is_multi_machine = len(worker_ips) > 1 or (len(worker_ips) == 1 and local_ip not in worker_ips)
        
        print(f"\n🎯 检测结果:")
        print(f"  工作节点数: {len(workers)}")
        print(f"  不同IP数: {len(worker_ips)}")
        print(f"  工作节点IP: {list(worker_ips)}")
        
        if is_multi_machine:
            print("  🎉 ✅ 真正的多机分布式部署")
        else:
            print("  ⚠️  ❌ 单机分布式部署")
            print("  💡 所有工作节点都在本机上")
        
        return is_multi_machine
        
    except Exception as e:
        print(f"❌ 连接集群失败: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="端口检测工具")
    parser.add_argument("--scheduler", type=str, default="tcp://***********:8786",
                       help="调度器地址")
    
    args = parser.parse_args()
    
    print("🚀 开始检测...")
    is_multi = check_multi_machine(args.scheduler)
    
    print("\n" + "=" * 40)
    if is_multi:
        print("🎉 恭喜！您的部署是真正的多机分布式")
        print("✅ 可以充分利用多台机器的计算资源")
    else:
        print("💡 当前是单机分布式模式")
        print("🔧 要实现多机分布式，请在另一台电脑运行:")
        print(f"   dask-worker {args.scheduler} --memory-limit 2GB --nthreads 2")

if __name__ == "__main__":
    main()
